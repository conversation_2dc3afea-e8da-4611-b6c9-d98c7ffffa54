2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 开始操作: Excel文件处理
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - [Excel文件处理] 步骤 1: 读取Excel文件
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 文件操作: 读取 - test.xlsx - 成功 - 文件大小: 2.5MB
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - [Excel文件处理] 步骤 2: 数据清洗和格式化
2025-07-15 17:04:58 - ExcelTools - DEBUG - log_module.py:96 - debug - 开始数据清洗...
2025-07-15 17:04:58 - ExcelTools - DEBUG - log_module.py:96 - debug - 移除空行: 5行
2025-07-15 17:04:58 - ExcelTools - DEBUG - log_module.py:96 - debug - 格式化日期列: Created
2025-07-15 17:04:58 - ExcelTools - DEBUG - log_module.py:96 - debug - 标准化Package名称
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - [Excel文件处理] 步骤 3: 生成分析报告
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 生成Package分布统计
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 生成团队分布统计
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 生成异常关键词分析
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - [Excel文件处理] 步骤 4: 保存结果文件
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 文件操作: 保存 - analysis_results_20250715.xlsx - 成功 - 包含5个工作表
2025-07-15 17:04:58 - ExcelTools - INFO - log_module.py:100 - info - 操作完成: Excel文件处理 - Excel文件处理完成 (耗时: 0.003s, 步骤数: 4)
