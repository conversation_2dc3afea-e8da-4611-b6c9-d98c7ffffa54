2025-07-15 17:03:48 - INFO - 日志模块测试开始
2025-07-15 17:03:48 - WARNING - 这是警告信息
2025-07-15 17:03:48 - ERROR - 这是错误信息
2025-07-15 17:03:48 - INFO - 开始操作: Excel文件分析
2025-07-15 17:03:48 - INFO - [Excel文件分析] 步骤 1: 读取文件
2025-07-15 17:03:48 - INFO - [Excel文件分析] 步骤 2: 解析数据
2025-07-15 17:03:48 - INFO - [Excel文件分析] 步骤 3: 生成报告
2025-07-15 17:03:48 - INFO - 操作完成: Excel文件分析 - 分析完成，共处理100条记录 (耗时: 0.000s, 步骤数: 3)
2025-07-15 17:03:48 - INFO - 文件操作: 读取 - test.xlsx - 成功 - 文件大小: 1.2MB
2025-07-15 17:03:48 - INFO - Excel分析: test.xlsx - 成功 - 100 条记录
2025-07-15 17:03:48 - INFO - 批量文件分析: 总计 10, 成功 8, 失败 2
2025-07-15 17:03:48 - INFO - 日志统计: {'log_dir': 'D:\\PythonProject\\transsiongroot\\task_manager\\backend\\logs', 'log_files': [{'name': 'TestLogger_20250715.log', 'size': 822, 'size_mb': 0.0, 'modified': '2025-07-15 17:03:48'}, {'name': 'TestLogger_detailed.log', 'size': 1356, 'size_mb': 0.0, 'modified': '2025-07-15 17:03:48'}, {'name': 'TestLogger_error.log', 'size': 91, 'size_mb': 0.0, 'modified': '2025-07-15 17:03:48'}, {'name': 'TranssionTools_20250715.log', 'size': 0, 'size_mb': 0.0, 'modified': '2025-07-15 17:03:48'}, {'name': 'TranssionTools_detailed.log', 'size': 0, 'size_mb': 0.0, 'modified': '2025-07-15 17:03:48'}, {'name': 'TranssionTools_error.log', 'size': 0, 'size_mb': 0.0, 'modified': '2025-07-15 17:03:48'}], 'total_size': 2269, 'total_size_mb': 0.0}
2025-07-15 17:03:48 - INFO - 日志模块测试完成
