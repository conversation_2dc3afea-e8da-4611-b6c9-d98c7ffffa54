#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
飞书多维表格操作工具模块
功能：
1. 打开多维表格
2. 获取指定记录（支持条件筛选）
3. 生成词条记录的分享链接
4. 支持分页查询和批量操作

作者：资深测试开发
创建时间：2025-07-21
"""

import json
import re
import urllib.parse
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urlparse, parse_qs

import requests
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

from .log_module import LogManager


class FeishuBitableError(Exception):
    """飞书多维表格操作异常"""
    pass


class FeishuBitableTools:
    """
    飞书多维表格操作工具类
    
    支持功能：
    - 解析多维表格URL
    - 获取表格数据
    - 条件筛选记录
    - 生成分享链接
    """
    
    def __init__(self, app_id: str, app_secret: str, log_name: str = "FeishuBitable"):
        """
        初始化飞书多维表格工具
        
        Args:
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
            log_name: 日志名称
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.logger = LogManager(log_name).get_logger()
        
        # 初始化飞书客户端
        self.client = lark.Client.builder() \
            .app_id(app_id) \
            .app_secret(app_secret) \
            .log_level(lark.LogLevel.INFO) \
            .build()
        
        self._tenant_access_token = None
        self._token_expire_time = 0
        
        self.logger.info(f"飞书多维表格工具初始化完成，应用ID: {app_id}")
    
    def _get_tenant_access_token(self) -> str:
        """
        获取tenant_access_token
        
        Returns:
            str: 访问令牌
        """
        import time
        
        # 检查token是否过期（提前5分钟刷新）
        if self._tenant_access_token and time.time() < (self._token_expire_time - 300):
            return self._tenant_access_token
        
        try:
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }
            headers = {'Content-Type': 'application/json'}
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') != 0:
                raise FeishuBitableError(f"获取访问令牌失败: {result.get('msg', '未知错误')}")
            
            self._tenant_access_token = result['tenant_access_token']
            self._token_expire_time = time.time() + result.get('expire', 7200)
            
            self.logger.info("成功获取tenant_access_token")
            return self._tenant_access_token
            
        except requests.RequestException as e:
            self.logger.error(f"获取访问令牌网络请求失败: {str(e)}")
            raise FeishuBitableError(f"网络请求失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"获取访问令牌失败: {str(e)}")
            raise FeishuBitableError(f"获取访问令牌失败: {str(e)}")
    
    def parse_bitable_url(self, url: str) -> Dict[str, str]:
        """
        解析飞书多维表格URL，提取app_token、table_id、view_id等参数
        
        Args:
            url: 飞书多维表格URL
            
        Returns:
            Dict[str, str]: 包含app_token、table_id、view_id等参数的字典
            
        Example:
            url = "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI"
            result = parse_bitable_url(url)
            # result = {
            #     'app_token': 'T6epbT9jwa4mB4sEx6EcdwDwnlb',
            #     'table_id': 'tbl9ZF2ZIqckMKDE',
            #     'view_id': 'vewqsVNulI'
            # }
        """
        try:
            self.logger.info(f"开始解析多维表格URL: {url}")
            
            # 解析URL
            parsed_url = urlparse(url)
            
            # 提取app_token（从路径中获取）
            path_parts = parsed_url.path.strip('/').split('/')
            if len(path_parts) < 2 or path_parts[0] != 'base':
                raise FeishuBitableError("无效的多维表格URL格式")
            
            app_token = path_parts[1]
            
            # 解析查询参数
            query_params = parse_qs(parsed_url.query)
            
            result = {
                'app_token': app_token,
                'table_id': query_params.get('table', [''])[0],
                'view_id': query_params.get('view', [''])[0],
                'base_url': f"{parsed_url.scheme}://{parsed_url.netloc}"
            }
            
            # 验证必要参数
            if not result['app_token']:
                raise FeishuBitableError("无法从URL中提取app_token")
            
            if not result['table_id']:
                self.logger.warning("URL中未包含table_id参数")
            
            self.logger.info(f"URL解析成功: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"解析多维表格URL失败: {str(e)}")
            raise FeishuBitableError(f"解析URL失败: {str(e)}")
    
    def open_bitable(self, url: str) -> Dict[str, Any]:
        """
        打开多维表格，获取表格基本信息
        
        Args:
            url: 飞书多维表格URL
            
        Returns:
            Dict[str, Any]: 表格信息
        """
        try:
            # 解析URL获取参数
            url_params = self.parse_bitable_url(url)
            app_token = url_params['app_token']
            
            self.logger.info(f"开始打开多维表格: {app_token}")
            
            # 获取访问令牌
            token = self._get_tenant_access_token()
            
            # 获取多维表格元数据
            request_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}"
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(request_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') != 0:
                raise FeishuBitableError(f"获取表格信息失败: {result.get('msg', '未知错误')}")
            
            app_info = result['data']['app']
            
            # 获取表格列表
            tables_info = self.get_tables_info(app_token)
            
            bitable_info = {
                'app_token': app_token,
                'app_name': app_info.get('name', ''),
                'app_description': app_info.get('description', ''),
                'tables': tables_info,
                'url_params': url_params
            }
            
            self.logger.info(f"成功打开多维表格: {app_info.get('name', app_token)}")
            return bitable_info
            
        except Exception as e:
            self.logger.error(f"打开多维表格失败: {str(e)}")
            raise FeishuBitableError(f"打开多维表格失败: {str(e)}")
    
    def get_tables_info(self, app_token: str) -> List[Dict[str, Any]]:
        """
        获取多维表格中所有数据表信息
        
        Args:
            app_token: 多维表格token
            
        Returns:
            List[Dict[str, Any]]: 数据表信息列表
        """
        try:
            token = self._get_tenant_access_token()
            
            request_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables"
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(request_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') != 0:
                raise FeishuBitableError(f"获取数据表信息失败: {result.get('msg', '未知错误')}")
            
            tables = result['data']['items']
            self.logger.info(f"获取到 {len(tables)} 个数据表")
            
            return tables
            
        except Exception as e:
            self.logger.error(f"获取数据表信息失败: {str(e)}")
            raise FeishuBitableError(f"获取数据表信息失败: {str(e)}")

    def get_records(self, app_token: str, table_id: str,
                   filter_conditions: Optional[Dict[str, Any]] = None,
                   view_id: Optional[str] = None,
                   page_size: int = 100,
                   page_token: Optional[str] = None) -> Dict[str, Any]:
        """
        获取多维表格记录

        Args:
            app_token: 多维表格token
            table_id: 数据表ID
            filter_conditions: 筛选条件
            view_id: 视图ID
            page_size: 每页记录数，最大500
            page_token: 分页标记

        Returns:
            Dict[str, Any]: 包含记录数据和分页信息
        """
        try:
            self.logger.info(f"开始获取记录: app_token={app_token}, table_id={table_id}")

            token = self._get_tenant_access_token()

            request_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/search"
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            # 构建请求体
            payload = {
                'page_size': min(page_size, 500)  # 限制最大页面大小
            }

            if view_id:
                payload['view_id'] = view_id

            if page_token:
                payload['page_token'] = page_token

            if filter_conditions:
                payload['filter'] = filter_conditions

            response = requests.post(request_url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            if result.get('code') != 0:
                raise FeishuBitableError(f"获取记录失败: {result.get('msg', '未知错误')}")

            data = result['data']
            records = data.get('items', [])

            self.logger.info(f"成功获取 {len(records)} 条记录")

            return {
                'records': records,
                'has_more': data.get('has_more', False),
                'page_token': data.get('page_token', ''),
                'total': data.get('total', len(records))
            }

        except Exception as e:
            self.logger.error(f"获取记录失败: {str(e)}")
            raise FeishuBitableError(f"获取记录失败: {str(e)}")

    def get_records_by_condition(self, url: str, field_name: str, field_value: Any,
                               operator: str = "is") -> List[Dict[str, Any]]:
        """
        根据条件获取指定记录

        Args:
            url: 飞书多维表格URL
            field_name: 字段名称
            field_value: 字段值
            operator: 操作符，支持: is, isNot, contains, doesNotContain, isEmpty, isNotEmpty

        Returns:
            List[Dict[str, Any]]: 符合条件的记录列表

        Example:
            # 获取Level为L0的记录
            records = get_records_by_condition(url, "Level", "L0", "is")
        """
        try:
            # 解析URL
            url_params = self.parse_bitable_url(url)
            app_token = url_params['app_token']
            table_id = url_params['table_id']
            view_id = url_params.get('view_id')

            if not table_id:
                raise FeishuBitableError("URL中缺少table_id参数")

            self.logger.info(f"开始按条件查询记录: {field_name} {operator} {field_value}")

            # 构建筛选条件
            filter_conditions = {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": field_name,
                        "operator": operator,
                        "value": [field_value] if operator not in ["isEmpty", "isNotEmpty"] else []
                    }
                ]
            }

            # 获取所有符合条件的记录（处理分页）
            all_records = []
            page_token = None

            while True:
                result = self.get_records(
                    app_token=app_token,
                    table_id=table_id,
                    filter_conditions=filter_conditions,
                    view_id=view_id,
                    page_token=page_token
                )

                all_records.extend(result['records'])

                if not result['has_more']:
                    break

                page_token = result['page_token']

            self.logger.info(f"按条件查询完成，共找到 {len(all_records)} 条记录")
            return all_records

        except Exception as e:
            self.logger.error(f"按条件获取记录失败: {str(e)}")
            raise FeishuBitableError(f"按条件获取记录失败: {str(e)}")

    def generate_record_share_link(self, url: str, record_id: str) -> str:
        """
        生成词条记录的分享链接

        Args:
            url: 原始多维表格URL
            record_id: 记录ID

        Returns:
            str: 记录分享链接
        """
        try:
            # 解析原始URL
            url_params = self.parse_bitable_url(url)
            base_url = url_params['base_url']
            app_token = url_params['app_token']
            table_id = url_params['table_id']
            view_id = url_params.get('view_id', '')

            # 构建记录分享链接
            share_url = f"{base_url}/base/{app_token}"

            # 添加查询参数
            params = {
                'table': table_id,
                'record': record_id
            }

            if view_id:
                params['view'] = view_id

            # 构建完整URL
            query_string = urllib.parse.urlencode(params)
            share_link = f"{share_url}?{query_string}"

            self.logger.info(f"生成记录分享链接: {share_link}")
            return share_link

        except Exception as e:
            self.logger.error(f"生成分享链接失败: {str(e)}")
            raise FeishuBitableError(f"生成分享链接失败: {str(e)}")

    def get_all_records(self, url: str) -> List[Dict[str, Any]]:
        """
        获取多维表格中的所有记录（自动处理分页）

        Args:
            url: 飞书多维表格URL

        Returns:
            List[Dict[str, Any]]: 所有记录列表
        """
        try:
            url_params = self.parse_bitable_url(url)
            app_token = url_params['app_token']
            table_id = url_params['table_id']
            view_id = url_params.get('view_id')

            if not table_id:
                raise FeishuBitableError("URL中缺少table_id参数")

            self.logger.info("开始获取所有记录")

            all_records = []
            page_token = None

            while True:
                result = self.get_records(
                    app_token=app_token,
                    table_id=table_id,
                    view_id=view_id,
                    page_token=page_token,
                    page_size=500  # 使用最大页面大小提高效率
                )

                all_records.extend(result['records'])

                if not result['has_more']:
                    break

                page_token = result['page_token']

            self.logger.info(f"获取所有记录完成，共 {len(all_records)} 条")
            return all_records

        except Exception as e:
            self.logger.error(f"获取所有记录失败: {str(e)}")
            raise FeishuBitableError(f"获取所有记录失败: {str(e)}")

    def format_records_for_display(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化记录数据，便于显示和处理

        Args:
            records: 原始记录数据

        Returns:
            List[Dict[str, Any]]: 格式化后的记录数据
        """
        formatted_records = []

        for record in records:
            formatted_record = {
                'record_id': record.get('record_id', ''),
                'created_time': record.get('created_time', ''),
                'last_modified_time': record.get('last_modified_time', ''),
                'created_by': record.get('created_by', {}),
                'last_modified_by': record.get('last_modified_by', {}),
                'fields': {}
            }

            # 处理字段数据
            fields = record.get('fields', {})
            for field_name, field_value in fields.items():
                # 根据字段类型处理值
                if isinstance(field_value, list) and len(field_value) > 0:
                    # 处理多选、人员等列表类型字段
                    if isinstance(field_value[0], dict):
                        formatted_record['fields'][field_name] = [
                            item.get('text', item.get('name', str(item)))
                            for item in field_value
                        ]
                    else:
                        formatted_record['fields'][field_name] = field_value
                elif isinstance(field_value, dict):
                    # 处理单选、人员等对象类型字段
                    formatted_record['fields'][field_name] = field_value.get('text',
                                                                           field_value.get('name', str(field_value)))
                else:
                    formatted_record['fields'][field_name] = field_value

            formatted_records.append(formatted_record)

        return formatted_records

    def search_records_by_multiple_conditions(self, url: str,
                                            conditions: List[Dict[str, Any]],
                                            conjunction: str = "and") -> List[Dict[str, Any]]:
        """
        根据多个条件搜索记录

        Args:
            url: 飞书多维表格URL
            conditions: 条件列表，每个条件包含field_name, operator, value
            conjunction: 条件连接符，"and" 或 "or"

        Returns:
            List[Dict[str, Any]]: 符合条件的记录列表

        Example:
            conditions = [
                {"field_name": "Level", "operator": "is", "value": "L0"},
                {"field_name": "Status", "operator": "is", "value": "Active"}
            ]
            records = search_records_by_multiple_conditions(url, conditions, "and")
        """
        try:
            url_params = self.parse_bitable_url(url)
            app_token = url_params['app_token']
            table_id = url_params['table_id']
            view_id = url_params.get('view_id')

            if not table_id:
                raise FeishuBitableError("URL中缺少table_id参数")

            self.logger.info(f"开始多条件搜索，条件数: {len(conditions)}, 连接符: {conjunction}")

            # 构建筛选条件
            filter_conditions = {
                "conjunction": conjunction,
                "conditions": []
            }

            for condition in conditions:
                filter_condition = {
                    "field_name": condition["field_name"],
                    "operator": condition["operator"]
                }

                # 根据操作符决定是否需要值
                if condition["operator"] not in ["isEmpty", "isNotEmpty"]:
                    value = condition["value"]
                    filter_condition["value"] = [value] if not isinstance(value, list) else value

                filter_conditions["conditions"].append(filter_condition)

            # 获取所有符合条件的记录
            all_records = []
            page_token = None

            while True:
                result = self.get_records(
                    app_token=app_token,
                    table_id=table_id,
                    filter_conditions=filter_conditions,
                    view_id=view_id,
                    page_token=page_token
                )

                all_records.extend(result['records'])

                if not result['has_more']:
                    break

                page_token = result['page_token']

            self.logger.info(f"多条件搜索完成，共找到 {len(all_records)} 条记录")
            return all_records

        except Exception as e:
            self.logger.error(f"多条件搜索失败: {str(e)}")
            raise FeishuBitableError(f"多条件搜索失败: {str(e)}")
