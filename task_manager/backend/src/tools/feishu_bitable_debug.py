#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
飞书多维表格权限调试工具
用于诊断和解决权限问题
"""

import json
from feishu_bitable_tools import FeishuBitableTools, FeishuBitableError, _make_request
from feishu_bitable_config import get_config


def check_app_credentials():
    """检查应用凭据是否有效"""
    print("🔐 检查应用凭据...")
    
    config = get_config()
    app_id = config["APP_ID"]
    app_secret = config["APP_SECRET"]

    try:
        # 尝试获取应用访问令牌
        url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
        payload = {
            "app_id": app_id,
            "app_secret": app_secret
        }
        headers = {'Content-Type': 'application/json'}

        response = _make_request('POST', url, headers=headers, json_data=payload)
        result = response.json()

        if result.get('code') == 0:
            print("✅ 应用凭据有效")
            print(f"   App ID: {app_id}")
            print(f"   Token有效期: {result.get('expire', 'N/A')} 秒")
            return True, result['app_access_token']
        else:
            print("❌ 应用凭据无效")
            print(f"   错误代码: {result.get('code')}")
            print(f"   错误信息: {result.get('msg')}")
            return False, None
            
    except Exception as e:
        print(f"❌ 检查凭据时出错: {str(e)}")
        return False, None


def check_app_permissions(token):
    """检查应用权限"""
    print("\n🔑 检查应用权限...")
    
    try:
        # 获取应用信息
        url = "https://open.feishu.cn/open-apis/application/v6/app"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = _make_request('GET', url, headers=headers)
        result = response.json()
        
        if result.get('code') == 0:
            app_info = result['data']['app']
            print("✅ 应用信息获取成功")
            print(f"   应用名称: {app_info.get('app_name', 'N/A')}")
            print(f"   应用状态: {app_info.get('status', 'N/A')}")
            print(f"   应用类型: {app_info.get('app_type', 'N/A')}")
            
            # 检查权限范围
            scopes = app_info.get('scope', {})
            if scopes:
                print("   权限范围:")
                for scope_type, scope_list in scopes.items():
                    print(f"     {scope_type}: {scope_list}")
            
            return True
        else:
            print("❌ 无法获取应用信息")
            print(f"   错误代码: {result.get('code')}")
            print(f"   错误信息: {result.get('msg')}")
            return False
            
    except Exception as e:
        print(f"❌ 检查权限时出错: {str(e)}")
        return False


def test_bitable_access(token, app_token):
    """测试多维表格访问权限"""
    print(f"\n📊 测试多维表格访问权限...")
    print(f"   目标表格: {app_token}")
    
    try:
        # 尝试获取表格信息
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = _make_request('GET', url, headers=headers)
        result = response.json()
        
        if result.get('code') == 0:
            app_info = result['data']['app']
            print("✅ 多维表格访问成功")
            print(f"   表格名称: {app_info.get('name', 'N/A')}")
            print(f"   表格描述: {app_info.get('description', 'N/A')}")
            return True
        else:
            print("❌ 多维表格访问失败")
            print(f"   错误代码: {result.get('code')}")
            print(f"   错误信息: {result.get('msg')}")
            
            # 提供具体的错误解释
            error_code = result.get('code')
            if error_code == 403:
                print("\n💡 403错误可能的原因:")
                print("   1. 应用没有访问此多维表格的权限")
                print("   2. 需要在多维表格中添加应用为协作者")
                print("   3. 应用权限范围不包含多维表格相关权限")
            elif error_code == 404:
                print("\n💡 404错误可能的原因:")
                print("   1. 多维表格不存在或已被删除")
                print("   2. app_token不正确")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试访问时出错: {str(e)}")
        return False


def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案:")
    print("=" * 50)
    
    print("\n1. 检查应用权限配置:")
    print("   - 登录飞书开放平台 (https://open.feishu.cn)")
    print("   - 进入应用管理 -> 选择您的应用")
    print("   - 检查权限管理，确保包含以下权限:")
    print("     • bitable:app (多维表格应用权限)")
    print("     • bitable:app:readonly (多维表格只读权限)")
    print("   - 发布应用版本")
    
    print("\n2. 在多维表格中添加应用:")
    print("   - 打开目标多维表格")
    print("   - 点击右上角「...」-> 「高级」-> 「开放平台」")
    print("   - 添加您的应用为协作者")
    print("   - 给予适当的权限（至少需要查看权限）")
    
    print("\n3. 检查应用配置:")
    print("   - 确认 APP_ID 和 APP_SECRET 正确")
    print("   - 确认应用已发布且状态正常")
    print("   - 确认多维表格的 app_token 正确")
    
    print("\n4. 测试步骤:")
    print("   - 先用简单的API测试应用权限")
    print("   - 再测试多维表格访问权限")
    print("   - 逐步增加功能复杂度")


def main():
    """主诊断流程"""
    print("🚀 飞书多维表格权限诊断工具")
    print("=" * 50)
    
    # 1. 检查应用凭据
    credentials_ok, token = check_app_credentials()
    
    if not credentials_ok:
        print("\n❌ 应用凭据检查失败，请检查 APP_ID 和 APP_SECRET")
        provide_solutions()
        return
    
    # 2. 检查应用权限
    permissions_ok = check_app_permissions(token)
    
    # 3. 测试多维表格访问
    config = get_config()
    url = config["DEFAULT_BITABLE_URL"]
    
    # 从URL中提取app_token
    try:
        bitable_tools = FeishuBitableTools("dummy", "dummy")
        url_params = bitable_tools.parse_bitable_url(url)
        app_token = url_params['app_token']
        
        bitable_ok = test_bitable_access(token, app_token)
        
    except Exception as e:
        print(f"❌ URL解析失败: {str(e)}")
        bitable_ok = False
    
    # 4. 总结和建议
    print("\n" + "=" * 50)
    print("📋 诊断总结:")
    print(f"   应用凭据: {'✅ 正常' if credentials_ok else '❌ 异常'}")
    print(f"   应用权限: {'✅ 正常' if permissions_ok else '❌ 异常'}")
    print(f"   表格访问: {'✅ 正常' if bitable_ok else '❌ 异常'}")
    
    if not bitable_ok:
        provide_solutions()
    else:
        print("\n🎉 所有检查通过！您的应用配置正常。")


if __name__ == "__main__":
    main()
