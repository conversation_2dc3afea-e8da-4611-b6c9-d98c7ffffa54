#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
飞书多维表格操作演示版本
不需要实际API权限，展示所有功能的使用方法
"""

import json
from datetime import datetime
from feishu_bitable_tools import FeishuBitableTools


def create_mock_data():
    """创建模拟数据"""
    return [
        {
            "record_id": "rec001",
            "created_time": "2025-07-21T10:00:00Z",
            "last_modified_time": "2025-07-21T10:30:00Z",
            "fields": {
                "Level": "L0",
                "Status": "Active",
                "Name": "测试词条1",
                "Description": "这是一个L0级别的测试词条",
                "Priority": "High"
            }
        },
        {
            "record_id": "rec002", 
            "created_time": "2025-07-21T09:00:00Z",
            "last_modified_time": "2025-07-21T09:45:00Z",
            "fields": {
                "Level": "L0",
                "Status": "Active", 
                "Name": "测试词条2",
                "Description": "另一个L0级别的词条",
                "Priority": "Medium"
            }
        },
        {
            "record_id": "rec003",
            "created_time": "2025-07-21T08:00:00Z", 
            "last_modified_time": "2025-07-21T08:20:00Z",
            "fields": {
                "Level": "L1",
                "Status": "Active",
                "Name": "测试词条3", 
                "Description": "这是一个L1级别的词条",
                "Priority": "Low"
            }
        },
        {
            "record_id": "rec004",
            "created_time": "2025-07-21T07:00:00Z",
            "last_modified_time": "2025-07-21T07:15:00Z", 
            "fields": {
                "Level": "L0",
                "Status": "Deleted",
                "Name": "已删除词条",
                "Description": "这个词条已被删除",
                "Priority": "Low"
            }
        }
    ]


def demo_url_parsing():
    """演示URL解析功能"""
    print("🔍 1. URL解析功能演示")
    print("-" * 30)
    
    # 创建工具实例（使用演示凭据）
    bitable_tools = FeishuBitableTools("demo_app_id", "demo_app_secret")
    
    # 演示URL
    url = "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI"
    
    try:
        url_params = bitable_tools.parse_bitable_url(url)
        print("✅ URL解析成功:")
        print(f"   📊 App Token: {url_params['app_token']}")
        print(f"   📋 Table ID: {url_params['table_id']}")
        print(f"   👁️ View ID: {url_params['view_id']}")
        print(f"   🌐 Base URL: {url_params['base_url']}")
        return url_params
    except Exception as e:
        print(f"❌ URL解析失败: {e}")
        return None


def demo_record_filtering():
    """演示记录筛选功能"""
    print("\n📋 2. 记录筛选功能演示")
    print("-" * 30)
    
    # 获取模拟数据
    mock_records = create_mock_data()
    print(f"📊 总记录数: {len(mock_records)}")
    
    # 演示按Level筛选
    print("\n🔍 筛选Level为L0的记录:")
    l0_records = [r for r in mock_records if r['fields'].get('Level') == 'L0']
    print(f"   找到 {len(l0_records)} 条记录")
    
    for i, record in enumerate(l0_records, 1):
        print(f"   记录 {i}:")
        print(f"     ID: {record['record_id']}")
        print(f"     名称: {record['fields']['Name']}")
        print(f"     状态: {record['fields']['Status']}")
        print(f"     优先级: {record['fields']['Priority']}")
    
    # 演示多条件筛选
    print("\n🔍 筛选Level为L0且状态为Active的记录:")
    filtered_records = [
        r for r in mock_records 
        if r['fields'].get('Level') == 'L0' and r['fields'].get('Status') == 'Active'
    ]
    print(f"   找到 {len(filtered_records)} 条记录")
    
    return filtered_records


def demo_share_link_generation(url_params, records):
    """演示分享链接生成功能"""
    print("\n🔗 3. 分享链接生成演示")
    print("-" * 30)
    
    if not url_params:
        print("❌ 无法生成分享链接，URL参数缺失")
        return
    
    bitable_tools = FeishuBitableTools("demo_app_id", "demo_app_secret")
    base_url = "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI"
    
    print("🔗 为每条记录生成分享链接:")
    for i, record in enumerate(records, 1):
        try:
            share_link = bitable_tools.generate_record_share_link(
                base_url, record['record_id']
            )
            print(f"   记录 {i} ({record['fields']['Name']}):")
            print(f"     🔗 {share_link}")
        except Exception as e:
            print(f"   记录 {i} 链接生成失败: {e}")


def demo_data_formatting():
    """演示数据格式化功能"""
    print("\n📝 4. 数据格式化演示")
    print("-" * 30)
    
    mock_records = create_mock_data()
    bitable_tools = FeishuBitableTools("demo_app_id", "demo_app_secret")
    
    # 格式化记录
    formatted_records = bitable_tools.format_records_for_display(mock_records)
    
    print("📋 格式化后的记录数据:")
    for i, record in enumerate(formatted_records[:2], 1):  # 只显示前2条
        print(f"\n   记录 {i}:")
        print(f"     ID: {record['record_id']}")
        print(f"     创建时间: {record['created_time']}")
        print(f"     修改时间: {record['last_modified_time']}")
        print(f"     字段数据:")
        for field_name, field_value in record['fields'].items():
            print(f"       {field_name}: {field_value}")


def demo_search_operators():
    """演示搜索操作符功能"""
    print("\n🔧 5. 搜索操作符演示")
    print("-" * 30)
    
    mock_records = create_mock_data()
    
    # 演示不同操作符的效果
    operators_demo = [
        ("is", "Level", "L0", "等于L0"),
        ("isNot", "Status", "Deleted", "状态不等于Deleted"),
        ("contains", "Name", "测试", "名称包含'测试'"),
        ("isNotEmpty", "Description", None, "描述不为空")
    ]
    
    for operator, field, value, description in operators_demo:
        print(f"\n🔍 {description} (操作符: {operator}):")
        
        if operator == "is":
            filtered = [r for r in mock_records if r['fields'].get(field) == value]
        elif operator == "isNot":
            filtered = [r for r in mock_records if r['fields'].get(field) != value]
        elif operator == "contains":
            filtered = [r for r in mock_records if value in str(r['fields'].get(field, ''))]
        elif operator == "isNotEmpty":
            filtered = [r for r in mock_records if r['fields'].get(field)]
        else:
            filtered = []
        
        print(f"   找到 {len(filtered)} 条记录")
        for record in filtered:
            print(f"     - {record['fields']['Name']} (ID: {record['record_id']})")


def demo_statistics():
    """演示数据统计功能"""
    print("\n📊 6. 数据统计演示")
    print("-" * 30)
    
    mock_records = create_mock_data()
    
    # 按Level统计
    level_stats = {}
    for record in mock_records:
        level = record['fields'].get('Level', '未知')
        level_stats[level] = level_stats.get(level, 0) + 1
    
    print("📈 按Level统计:")
    for level, count in level_stats.items():
        print(f"   {level}: {count} 条记录")
    
    # 按Status统计
    status_stats = {}
    for record in mock_records:
        status = record['fields'].get('Status', '未知')
        status_stats[status] = status_stats.get(status, 0) + 1
    
    print("\n📈 按Status统计:")
    for status, count in status_stats.items():
        print(f"   {status}: {count} 条记录")
    
    # 按Priority统计
    priority_stats = {}
    for record in mock_records:
        priority = record['fields'].get('Priority', '未知')
        priority_stats[priority] = priority_stats.get(priority, 0) + 1
    
    print("\n📈 按Priority统计:")
    for priority, count in priority_stats.items():
        print(f"   {priority}: {count} 条记录")


def main():
    """主演示函数"""
    print("🚀 飞书多维表格操作工具 - 完整功能演示")
    print("=" * 60)
    print("💡 注意：这是演示版本，使用模拟数据展示所有功能")
    print("=" * 60)
    
    # 1. URL解析演示
    url_params = demo_url_parsing()
    
    # 2. 记录筛选演示
    filtered_records = demo_record_filtering()
    
    # 3. 分享链接生成演示
    demo_share_link_generation(url_params, filtered_records)
    
    # 4. 数据格式化演示
    demo_data_formatting()
    
    # 5. 搜索操作符演示
    demo_search_operators()
    
    # 6. 数据统计演示
    demo_statistics()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n💡 要使用真实数据，请:")
    print("   1. 配置正确的应用权限")
    print("   2. 在多维表格中添加应用为协作者")
    print("   3. 使用 feishu_bitable_usage_example.py 进行实际操作")
    print("=" * 60)


if __name__ == "__main__":
    main()
