# 飞书多维表格操作工具使用说明

## 概述

`feishu_bitable_tools.py` 是一个专门用于操作飞书多维表格的Python模块，提供了完整的多维表格操作功能，包括：

- 打开多维表格并获取基本信息
- 根据条件筛选记录
- 生成记录分享链接
- 支持分页查询和批量操作
- 集成日志记录功能

## 功能特性

### 1. 多维表格基本操作
- ✅ 解析多维表格URL，自动提取app_token、table_id、view_id等参数
- ✅ 获取多维表格元数据和数据表信息
- ✅ 自动处理访问令牌的获取和刷新

### 2. 记录查询功能
- ✅ 获取所有记录（自动处理分页）
- ✅ 根据单个条件筛选记录
- ✅ 根据多个条件组合筛选记录
- ✅ 支持多种筛选操作符

### 3. 分享链接生成
- ✅ 为特定记录生成分享链接
- ✅ 保持原有的视图和表格上下文

### 4. 数据处理
- ✅ 格式化记录数据，便于显示和处理
- ✅ 处理各种字段类型（文本、数字、选择、人员等）

## 安装依赖

确保已安装以下依赖包：

```bash
pip install lark-oapi requests
```

## 快速开始

### 1. 初始化工具

```python
from feishu_bitable_tools import FeishuBitableTools

# 初始化（需要替换为您的实际应用信息）
APP_ID = "your_app_id"
APP_SECRET = "your_app_secret"

bitable_tools = FeishuBitableTools(APP_ID, APP_SECRET)
```

### 2. 打开多维表格

```python
# 多维表格URL
url = "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI"

# 打开表格并获取信息
bitable_info = bitable_tools.open_bitable(url)
print(f"表格名称: {bitable_info['app_name']}")
print(f"数据表数量: {len(bitable_info['tables'])}")
```

### 3. 获取指定记录

```python
# 获取Level为L0的记录
records = bitable_tools.get_records_by_condition(
    url=url,
    field_name="Level",
    field_value="L0",
    operator="is"
)

print(f"找到 {len(records)} 条记录")
```

### 4. 生成分享链接

```python
# 为记录生成分享链接
for record in records:
    record_id = record['record_id']
    share_link = bitable_tools.generate_record_share_link(url, record_id)
    print(f"记录分享链接: {share_link}")
```

## 详细API说明

### FeishuBitableTools 类

#### 初始化参数
- `app_id`: 飞书应用ID
- `app_secret`: 飞书应用密钥
- `log_name`: 日志名称（可选，默认为"FeishuBitable"）

#### 主要方法

##### 1. `parse_bitable_url(url: str) -> Dict[str, str]`
解析飞书多维表格URL，提取关键参数。

**参数:**
- `url`: 飞书多维表格URL

**返回:**
```python
{
    'app_token': 'T6epbT9jwa4mB4sEx6EcdwDwnlb',
    'table_id': 'tbl9ZF2ZIqckMKDE',
    'view_id': 'vewqsVNulI',
    'base_url': 'https://transsioner.feishu.cn'
}
```

##### 2. `open_bitable(url: str) -> Dict[str, Any]`
打开多维表格，获取表格基本信息。

**参数:**
- `url`: 飞书多维表格URL

**返回:**
包含表格名称、描述、数据表列表等信息的字典。

##### 3. `get_records_by_condition(url, field_name, field_value, operator="is")`
根据条件获取指定记录。

**参数:**
- `url`: 飞书多维表格URL
- `field_name`: 字段名称
- `field_value`: 字段值
- `operator`: 操作符，支持以下值：
  - `"is"`: 等于
  - `"isNot"`: 不等于
  - `"contains"`: 包含
  - `"doesNotContain"`: 不包含
  - `"isEmpty"`: 为空
  - `"isNotEmpty"`: 不为空

**示例:**
```python
# 获取Level为L0的记录
records = bitable_tools.get_records_by_condition(url, "Level", "L0", "is")

# 获取名称包含"测试"的记录
records = bitable_tools.get_records_by_condition(url, "Name", "测试", "contains")

# 获取状态不为空的记录
records = bitable_tools.get_records_by_condition(url, "Status", None, "isNotEmpty")
```

##### 4. `search_records_by_multiple_conditions(url, conditions, conjunction="and")`
根据多个条件搜索记录。

**参数:**
- `url`: 飞书多维表格URL
- `conditions`: 条件列表
- `conjunction`: 条件连接符，"and" 或 "or"

**示例:**
```python
conditions = [
    {"field_name": "Level", "operator": "is", "value": "L0"},
    {"field_name": "Status", "operator": "isNot", "value": "Deleted"}
]
records = bitable_tools.search_records_by_multiple_conditions(url, conditions, "and")
```

##### 5. `generate_record_share_link(url: str, record_id: str) -> str`
生成记录分享链接。

**参数:**
- `url`: 原始多维表格URL
- `record_id`: 记录ID

**返回:**
记录的分享链接字符串。

##### 6. `get_all_records(url: str) -> List[Dict[str, Any]]`
获取多维表格中的所有记录（自动处理分页）。

##### 7. `format_records_for_display(records) -> List[Dict[str, Any]]`
格式化记录数据，便于显示和处理。

## 使用示例

### 完整示例

```python
from feishu_bitable_tools import FeishuBitableTools, FeishuBitableError

def main():
    # 配置信息
    APP_ID = "your_app_id"
    APP_SECRET = "your_app_secret"
    BITABLE_URL = "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI"
    
    try:
        # 初始化工具
        bitable_tools = FeishuBitableTools(APP_ID, APP_SECRET)
        
        # 1. 打开多维表格
        bitable_info = bitable_tools.open_bitable(BITABLE_URL)
        print(f"表格名称: {bitable_info['app_name']}")
        
        # 2. 获取Level为L0的记录
        l0_records = bitable_tools.get_records_by_condition(
            url=BITABLE_URL,
            field_name="Level",
            field_value="L0"
        )
        
        # 3. 格式化并显示记录
        formatted_records = bitable_tools.format_records_for_display(l0_records)
        for record in formatted_records:
            print(f"记录ID: {record['record_id']}")
            print(f"字段数据: {record['fields']}")
            
            # 4. 生成分享链接
            share_link = bitable_tools.generate_record_share_link(
                BITABLE_URL, record['record_id']
            )
            print(f"分享链接: {share_link}")
            print("-" * 50)
            
    except FeishuBitableError as e:
        print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
```

## 错误处理

模块定义了 `FeishuBitableError` 异常类，用于处理飞书多维表格操作相关的错误。建议在使用时进行适当的异常处理：

```python
try:
    records = bitable_tools.get_records_by_condition(url, "Level", "L0")
except FeishuBitableError as e:
    print(f"飞书操作错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 日志记录

模块集成了项目的日志系统，会自动记录操作日志。日志文件位于项目的logs目录下。

## 注意事项

1. **应用权限**: 确保您的飞书应用具有访问多维表格的权限
2. **URL格式**: 确保提供的多维表格URL格式正确
3. **字段名称**: 字段名称需要与多维表格中的实际字段名称完全匹配
4. **分页处理**: 对于大量数据，模块会自动处理分页，但可能需要较长时间
5. **访问令牌**: 模块会自动管理访问令牌的获取和刷新

## 支持的字段类型

- 文本
- 数字
- 单选
- 多选
- 日期
- 人员
- 附件
- 链接
- 公式
- 等等

## 更新日志

- v1.0.0: 初始版本，支持基本的多维表格操作功能
