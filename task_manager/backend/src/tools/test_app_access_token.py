#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试app_access_token的实际API调用
验证token获取和基本API访问功能
"""

from feishu_bitable_tools import FeishuBitableTools, FeishuBitableError
from feishu_bitable_config import get_config


def test_token_acquisition():
    """测试token获取"""
    print("🔐 测试app_access_token获取...")
    
    config = get_config()
    
    try:
        # 初始化工具
        bitable_tools = FeishuBitableTools(
            app_id=config["APP_ID"],
            app_secret=config["APP_SECRET"]
        )
        
        # 尝试获取token
        token = bitable_tools._get_app_access_token()
        
        print("✅ app_access_token获取成功")
        print(f"   Token长度: {len(token)} 字符")
        print(f"   Token前缀: {token[:20]}...")
        
        return True, bitable_tools
        
    except FeishuBitableError as e:
        print(f"❌ Token获取失败: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False, None


def test_url_parsing(bitable_tools):
    """测试URL解析功能"""
    print("\n🔍 测试URL解析功能...")
    
    config = get_config()
    url = config["DEFAULT_BITABLE_URL"]
    
    try:
        url_params = bitable_tools.parse_bitable_url(url)
        
        print("✅ URL解析成功")
        print(f"   App Token: {url_params['app_token']}")
        print(f"   Table ID: {url_params['table_id']}")
        print(f"   View ID: {url_params['view_id']}")
        
        return True, url_params
        
    except Exception as e:
        print(f"❌ URL解析失败: {e}")
        return False, None


def test_share_link_generation(bitable_tools, url_params):
    """测试分享链接生成"""
    print("\n🔗 测试分享链接生成...")
    
    if not url_params:
        print("❌ 无法测试，URL参数缺失")
        return False
    
    try:
        # 使用示例记录ID
        test_record_id = "rec123456789"
        config = get_config()
        url = config["DEFAULT_BITABLE_URL"]
        
        share_link = bitable_tools.generate_record_share_link(url, test_record_id)
        
        print("✅ 分享链接生成成功")
        print(f"   链接: {share_link}")
        
        # 验证链接格式
        expected_parts = [
            url_params['base_url'],
            url_params['app_token'],
            url_params['table_id'],
            test_record_id
        ]
        
        all_parts_present = all(part in share_link for part in expected_parts)
        
        if all_parts_present:
            print("✅ 链接格式验证通过")
            return True
        else:
            print("❌ 链接格式验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 分享链接生成失败: {e}")
        return False


def test_api_call_attempt(bitable_tools):
    """尝试实际API调用（可能会因权限问题失败）"""
    print("\n📊 尝试实际API调用...")
    
    config = get_config()
    url = config["DEFAULT_BITABLE_URL"]
    
    try:
        # 尝试打开多维表格
        bitable_info = bitable_tools.open_bitable(url)
        
        print("✅ 多维表格访问成功！")
        print(f"   表格名称: {bitable_info['app_name']}")
        print(f"   数据表数量: {len(bitable_info['tables'])}")
        
        return True
        
    except FeishuBitableError as e:
        if "403" in str(e):
            print("⚠️ 403权限错误（预期中）")
            print("   这表明app_access_token正常工作，但需要配置权限")
            print("   请按照文档配置应用权限或在多维表格中添加应用")
            return "permission_needed"
        else:
            print(f"❌ API调用失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False


def provide_next_steps():
    """提供后续步骤指导"""
    print("\n📋 后续步骤:")
    print("=" * 50)
    
    print("\n✅ app_access_token已正常工作")
    print("   现在需要配置权限以访问多维表格数据")
    
    print("\n🔧 权限配置方法:")
    print("   方法1 - 应用权限配置:")
    print("     1. 访问 https://open.feishu.cn")
    print("     2. 应用管理 → 选择应用")
    print("     3. 权限管理 → 添加多维表格权限")
    print("     4. 发布应用版本")
    
    print("\n   方法2 - 表格协作者:")
    print("     1. 打开目标多维表格")
    print("     2. 右上角「⋯」→「高级」→「开放平台」")
    print("     3. 添加应用为协作者")
    
    print("\n🎯 测试建议:")
    print("   1. 先配置权限")
    print("   2. 运行 python feishu_bitable_debug.py 验证")
    print("   3. 使用 python feishu_bitable_usage_example.py 测试完整功能")


def main():
    """主测试流程"""
    print("🚀 app_access_token 功能测试")
    print("=" * 50)
    
    # 1. 测试token获取
    token_ok, bitable_tools = test_token_acquisition()
    
    if not token_ok:
        print("\n❌ Token获取失败，请检查APP_ID和APP_SECRET")
        return
    
    # 2. 测试URL解析
    url_ok, url_params = test_url_parsing(bitable_tools)
    
    # 3. 测试分享链接生成
    if url_ok:
        link_ok = test_share_link_generation(bitable_tools, url_params)
    else:
        link_ok = False
    
    # 4. 尝试实际API调用
    api_result = test_api_call_attempt(bitable_tools)
    
    # 5. 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   Token获取: {'✅ 成功' if token_ok else '❌ 失败'}")
    print(f"   URL解析: {'✅ 成功' if url_ok else '❌ 失败'}")
    print(f"   分享链接: {'✅ 成功' if link_ok else '❌ 失败'}")
    
    if api_result == True:
        print(f"   API调用: ✅ 成功")
        print("\n🎉 所有功能正常！可以开始使用多维表格操作功能。")
    elif api_result == "permission_needed":
        print(f"   API调用: ⚠️ 需要权限配置")
        provide_next_steps()
    else:
        print(f"   API调用: ❌ 失败")
        print("\n❌ 需要检查配置或权限设置")


if __name__ == "__main__":
    main()
