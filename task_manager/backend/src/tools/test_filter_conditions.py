#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试筛选条件格式
验证修复后的筛选条件是否正确
"""

import json
from feishu_bitable_tools import FeishuBitableTools, FeishuBitableError
from feishu_bitable_config import get_config


def test_filter_condition_format():
    """测试筛选条件格式"""
    print("🔍 测试筛选条件格式...")
    
    config = get_config()
    
    try:
        # 初始化工具
        bitable_tools = FeishuBitableTools(
            app_id=config["APP_ID"],
            app_secret=config["APP_SECRET"]
        )
        
        url = config["DEFAULT_BITABLE_URL"]
        
        print("✅ 工具初始化成功")
        
        # 测试1: 简单条件筛选
        print("\n📋 测试1: 简单条件筛选 (Level = L0)")
        try:
            records = bitable_tools.get_records_by_condition(
                url=url,
                field_name="Level",
                field_value="L0",
                operator="is"
            )
            print(f"✅ 简单筛选成功，找到 {len(records)} 条记录")
            
        except FeishuBitableError as e:
            if "403" in str(e):
                print("⚠️ 403权限错误（预期中），但筛选条件格式正确")
            elif "InvalidFilter" in str(e):
                print(f"❌ 筛选条件格式错误: {e}")
                return False
            else:
                print(f"⚠️ 其他API错误: {e}")
        
        # 测试2: isEmpty操作符
        print("\n📋 测试2: isEmpty操作符")
        try:
            records = bitable_tools.get_records_by_condition(
                url=url,
                field_name="Description",
                field_value=None,
                operator="isEmpty"
            )
            print(f"✅ isEmpty筛选成功，找到 {len(records)} 条记录")
            
        except FeishuBitableError as e:
            if "403" in str(e):
                print("⚠️ 403权限错误（预期中），但筛选条件格式正确")
            elif "InvalidFilter" in str(e):
                print(f"❌ 筛选条件格式错误: {e}")
                return False
            else:
                print(f"⚠️ 其他API错误: {e}")
        
        # 测试3: 多条件筛选
        print("\n📋 测试3: 多条件筛选")
        try:
            conditions = [
                {"field_name": "Level", "operator": "is", "value": "L0"},
                {"field_name": "Status", "operator": "isNot", "value": "Deleted"}
            ]
            
            records = bitable_tools.search_records_by_multiple_conditions(
                url=url,
                conditions=conditions,
                conjunction="and"
            )
            print(f"✅ 多条件筛选成功，找到 {len(records)} 条记录")
            
        except FeishuBitableError as e:
            if "403" in str(e):
                print("⚠️ 403权限错误（预期中），但筛选条件格式正确")
            elif "InvalidFilter" in str(e):
                print(f"❌ 筛选条件格式错误: {e}")
                return False
            else:
                print(f"⚠️ 其他API错误: {e}")
        
        print("\n✅ 所有筛选条件格式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_filter_examples():
    """显示筛选条件示例"""
    print("\n📚 筛选条件格式示例:")
    print("-" * 40)
    
    examples = [
        {
            "title": "1. 等于条件",
            "condition": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "Level",
                        "operator": "is",
                        "value": ["L0"]
                    }
                ]
            }
        },
        {
            "title": "2. 不等于条件", 
            "condition": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "Status",
                        "operator": "isNot",
                        "value": ["Deleted"]
                    }
                ]
            }
        },
        {
            "title": "3. 为空条件",
            "condition": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "Description",
                        "operator": "isEmpty"
                    }
                ]
            }
        },
        {
            "title": "4. 多条件组合",
            "condition": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "Level",
                        "operator": "is", 
                        "value": ["L0"]
                    },
                    {
                        "field_name": "Status",
                        "operator": "isNot",
                        "value": ["Deleted"]
                    }
                ]
            }
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(json.dumps(example['condition'], indent=2, ensure_ascii=False))


def main():
    """主测试函数"""
    print("🚀 筛选条件格式测试")
    print("=" * 50)
    
    # 运行测试
    success = test_filter_condition_format()
    
    # 显示示例
    show_filter_examples()
    
    # 总结
    print("\n" + "=" * 50)
    if success:
        print("🎉 筛选条件格式修复成功！")
        print("\n💡 说明:")
        print("   - 修复了InvalidFilter错误")
        print("   - 确保value字段为字符串数组格式")
        print("   - 正确处理isEmpty/isNotEmpty操作符")
        print("   - 支持多条件组合筛选")
    else:
        print("❌ 筛选条件格式仍有问题，需要进一步调试")
    
    print("\n🔧 如果仍有权限问题，请:")
    print("   1. 配置应用权限")
    print("   2. 在多维表格中添加应用为协作者")
    print("   3. 使用 python feishu_bitable_debug.py 进行诊断")


if __name__ == "__main__":
    main()
