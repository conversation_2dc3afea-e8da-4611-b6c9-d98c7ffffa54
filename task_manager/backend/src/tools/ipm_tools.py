from datetime import datetime, timedelta
import json
import time

import requests
from task_manager.backend.src.tools.feishu_tools import FeishuBot


class IpmTools:
    def __init__(self):
        # self.headers = self.get_token()
        self.feishu_bot = FeishuBot()

    def get_token(self):
        url = "https://pfgateway.transsion.com:9199/uac-auth-service/v2/api/uac-auth/rtoken/get"

        payload = json.dumps({
            "utoken": "u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1"
        })
        headers = {
            'Content-Type': 'application/json',
            # 'Cookie': 'acw_tc=0a03846d17491919192118777e4f39e70605c924263c12224a29f2b2ddc11f'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        # print(response.text)
        token = response.json()['data']
        # 对字段进行字段转化
        tmp = {
            'P-Auth': token['rtoken'],
            'P-Rtoken': token['utoken'],
            'Content-Type': 'application/json',
        }
        return tmp

    def queryNoneSubmissionInfo(self, beginDate='2025-06-05', endDate='2025-06-06'):
        import requests
        import json

        url = "https://pfgatewaysz.transsion.com:9199/service-ipm-tsm/tsm/noneSubmission/queryNoneSubmissionInfo"

        payload = json.dumps({
            "count": True,
            "param": {
                "beginDate": beginDate,
                "endDate": endDate,
                "deptIdList": [
                    "704463829945810949"
                ]
            },
            "current": 1,
            "size": 20
        })
        # headers = {
        #     'P-Auth': 'r_OTQ3MWVyOHNuZXN6cjZhNjU1azVeOTA4MTkzNDkwMTk0MTM3OTIxMjE3MjA1',
        #     'P-Rtoken': 'u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1',
        #     'Content-Type': 'application/json',
        #     # 'Cookie': 'acw_tc=0a0011c017491936384277422e3e75c8475b6ef95d48b360cf4d15ef6923d3'
        # }
        headers = self.get_token()

        response = requests.request("POST", url, headers=headers, data=payload)

        # print(response.text)
        data = response.json().get('data', None)
        if data:
            return data
        else:
            raise Exception('查询无结果')

    def get_datatime(self):
        # 获取当前日期
        current_date = datetime.now()

        # 计算往前 7 天的日期
        seven_days_ago = current_date - timedelta(days=7)

        # 格式化为 "2025-06-06" 格式
        current_date_str = current_date.strftime("%Y-%m-%d")
        seven_days_ago_str = seven_days_ago.strftime("%Y-%m-%d")

        # print("当前日期:", current_date_str)
        # print("往前 7 天的日期:", seven_days_ago_str)

        return {
            'current_date_str': current_date_str,
            'seven_days_ago_str': seven_days_ago_str
        }

    def do_notify_none_submission(self):

        beginDate = self.get_datatime()['seven_days_ago_str']
        endDate = self.get_datatime()['current_date_str']

        data = self.queryNoneSubmissionInfo(beginDate, endDate)
        if len(data['data']) == 0: # 没有数据,无需通知
            return
        for item in data['data']:
            assignee = item['workerName']
            noneSubmissionDays = item['noneSubmissionDays']
            # dateList = item['dateList']

            if int(noneSubmissionDays) <=  1: # 未提报天数未超过1天,无需通知
                continue

            tmp = '{"text":"请及时处理未提报工时，当前已有' + str(noneSubmissionDays) + '天未提报工时 跳转链接'+'https://ipm.transsion.com' + '"}'

            # print(tmp)
            # if assignee=='卜昌义':
            #     self.feishu_bot.send_bug_notification_v5(assignee, tmp)
            #     break
            self.feishu_bot.send_bug_notification_v5(assignee, tmp)
            time.sleep(1)


if __name__ == '__main__':
    ipm = IpmTools()
    # ipm.get_datatime()
    ipm.do_notify_none_submission()
